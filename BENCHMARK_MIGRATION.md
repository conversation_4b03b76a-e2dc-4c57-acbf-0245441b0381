# Benchmark Organization Migration Guide

This document explains the reorganization of Gene's benchmark system and how to migrate from the old structure to the new organized structure.

## What Changed

### Old Structure (Scattered)
```
bench/                    # Mixed benchmarks
├── fibonacci.gene
├── loop_bench.gene
├── dict_bench.gene
└── run_benchmarks.nim

benchmarks/              # Allocation-focused
├── alloc_*.gene
├── pool_test.gene
└── simple_test.gene

scripts/                 # Mixed scripts
├── bench_*
├── fib_compare
└── profile_*

src/benchmark/           # Nim implementations
├── fibonacci.nim
├── arithmetic*.nim
└── *_profile.nim
```

### New Structure (Organized)
```
benchmarks_new/
├── computation/         # Computational algorithms
│   ├── fibonacci.gene
│   ├── fibonacci.nim
│   ├── loops.gene
│   ├── arithmetic*.nim
│   └── README.md
├── allocation/          # Memory management
│   ├── stress_test.gene
│   ├── pool_efficiency.gene
│   ├── alloc_*.gene
│   └── README.md
├── data_structures/     # Data structure operations
│   ├── array_operations.gene
│   ├── map_operations.gene
│   ├── string_operations.gene
│   └── README.md
├── vm_internals/        # VM implementation details
│   ├── tco.*
│   ├── *_profile.nim
│   └── README.md
├── comparison/          # Cross-language comparisons
│   ├── fibonacci_compare.sh
│   ├── compare_languages
│   └── README.md
├── scripts/             # Analysis and profiling tools
│   ├── bench_*
│   ├── profile*
│   └── benchme
├── runners/             # Unified execution system
│   ├── run_all.sh
│   ├── run_computation.sh
│   ├── run_allocation.sh
│   ├── run_data_structures.sh
│   └── run_vm_internals.sh
└── README.md
```

## Migration Steps

### 1. Update Build Scripts
If you have build scripts that reference the old benchmark locations:

**Old:**
```bash
./scripts/bench_suite
./scripts/fib_compare
nim c src/benchmark/fibonacci.nim
```

**New:**
```bash
./benchmarks_new/runners/run_all.sh
./benchmarks_new/comparison/fibonacci_compare.sh
nim c benchmarks_new/computation/fibonacci.nim
```

### 2. Update Documentation References
Replace references to old benchmark locations in documentation:

- `bench/` → `benchmarks_new/computation/` or appropriate category
- `benchmarks/` → `benchmarks_new/allocation/`
- `scripts/bench_*` → `benchmarks_new/scripts/` or `benchmarks_new/runners/`
- `src/benchmark/` → `benchmarks_new/vm_internals/` or appropriate category

### 3. Update CI/CD Pipelines
If you have automated testing that runs benchmarks:

**Old:**
```yaml
- run: ./scripts/bench_suite
- run: nim c -r src/benchmark/fibonacci.nim
```

**New:**
```yaml
- run: ./benchmarks_new/runners/run_all.sh
- run: ./benchmarks_new/runners/run_computation.sh
```

## New Usage Patterns

### Running All Benchmarks
```bash
# Run everything
./benchmarks_new/runners/run_all.sh

# Run with profiling
./benchmarks_new/runners/run_all.sh -p

# Run specific categories
./benchmarks_new/runners/run_all.sh -c computation,allocation

# Include cross-language comparisons
./benchmarks_new/runners/run_all.sh --compare
```

### Running Specific Categories
```bash
# Computational benchmarks only
./benchmarks_new/runners/run_computation.sh

# Memory allocation tests
./benchmarks_new/runners/run_allocation.sh

# Data structure performance
./benchmarks_new/runners/run_data_structures.sh

# VM internals and optimizations
./benchmarks_new/runners/run_vm_internals.sh
```

### Running Individual Benchmarks
```bash
# Gene benchmarks
gene benchmarks_new/computation/fibonacci.gene
gene benchmarks_new/allocation/stress_test.gene

# Compiled benchmarks
nim c -d:release -o:bin/fibonacci benchmarks_new/computation/fibonacci.nim
./bin/fibonacci
```

## Benefits of New Organization

### 1. **Clear Categorization**
- Easy to find benchmarks by functionality
- Logical grouping reduces confusion
- Better organization for new contributors

### 2. **Improved Documentation**
- Each category has its own README
- Clear performance expectations
- Better guidance for adding new benchmarks

### 3. **Unified Execution**
- Single entry point for all benchmarks
- Consistent command-line interface
- Integrated profiling and comparison options

### 4. **Better Maintenance**
- Easier to add new benchmark categories
- Clearer separation of concerns
- Reduced duplication

## Backward Compatibility

The old benchmark files remain in place for now to ensure backward compatibility. However, new development should use the new structure.

### Deprecation Timeline
1. **Phase 1** (Current): New structure available, old structure still works
2. **Phase 2** (Next release): Old structure marked as deprecated
3. **Phase 3** (Future release): Old structure removed

## Adding New Benchmarks

### Choose the Right Category
- **computation/**: Algorithms, math, loops, recursion
- **allocation/**: Memory management, GC, object lifecycle
- **data_structures/**: Arrays, maps, strings, collections
- **vm_internals/**: VM optimizations, frame management, instruction execution
- **comparison/**: Cross-language performance tests

### Follow the Standards
1. Add your benchmark file to the appropriate category
2. Update the category's README if needed
3. Add to the category's runner script
4. Include proper documentation and expected performance
5. Add comparison implementations if relevant

## Getting Help

If you need help with the migration:
1. Check the README files in each category
2. Look at existing benchmarks as examples
3. Use the unified runner scripts as templates
4. Ask questions in the project's issue tracker
