# Loop and arithmetic benchmark
(fn sum_loop n
  (var total 0)
  (for i 1 n
    (= total (+ total i)))
  total)

(fn count_loop n
  (var count 0)
  (while (< count n)
    (= count (+ count 1)))
  count)

# Run benchmarks
(var n 1000000)
(println "Loop benchmarks with n=" n)

(var start (time/now))
(var result (sum_loop n))
(var elapsed (- (time/now) start))
(println "sum_loop: " result " in " elapsed "ms")

(var start2 (time/now))
(var result2 (count_loop n))
(var elapsed2 (- (time/now) start2))
(println "count_loop: " result2 " in " elapsed2 "ms")