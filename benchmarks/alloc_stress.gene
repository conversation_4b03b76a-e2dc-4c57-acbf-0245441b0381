# Allocation stress test - creates many Gene objects, arrays, maps, and strings
# This should benefit from object pooling

(fn create_gene_tree depth
  (if (<= depth 0)
    (gene 'leaf)
    (gene 'node
      ^left  (create_gene_tree (- depth 1))
      ^right (create_gene_tree (- depth 1))
    )
  )
)

(fn create_string_list n
  (var result [])
  (for i (range n)
    (result .add (str "item_" i))
  )
  result
)

(fn create_map_chain n
  (var result {})
  (for i (range n)
    (result .set (str "key_" i) {^value i ^next nil})
  )
  result
)

(fn manipulate_arrays n
  (var arrays [])
  (for i (range n)
    (var arr [])
    (for j (range 10)
      (arr .add (* i j))
    )
    (arrays .add arr)
  )
  # Now merge them
  (var result [])
  (for arr arrays
    (for item arr
      (result .add item)
    )
  )
  result
)

# Benchmark 1: Gene tree creation (stresses Gene allocation)
(fn bench_gene_trees []
  (var total 0)
  (for i (range 1000)
    (var tree (create_gene_tree 8))  # Creates 2^8 = 256 Gene objects per iteration
    (total = (+ total 1))
  )
  total
)

# Benchmark 2: String operations (stresses String allocation)
(fn bench_strings []
  (var all_strings [])
  (for i (range 100)
    (var strings (create_string_list 100))
    (all_strings = (concat all_strings strings))
  )
  (all_strings .size)
)

# Benchmark 3: Map operations (stresses Map allocation via Reference pool)
(fn bench_maps []
  (var maps [])
  (for i (range 100)
    (maps .add (create_map_chain 50))
  )
  (maps .size)
)

# Benchmark 4: Array operations (stresses Array allocation via Reference pool)
(fn bench_arrays []
  (manipulate_arrays 100)
  .size
)

# Benchmark 5: Mixed allocation patterns
(fn bench_mixed []
  (var result {
    ^genes []
    ^strings []
    ^maps []
  })
  
  (for i (range 50)
    # Create various object types
    (result/genes .add (create_gene_tree 5))
    (result/strings = (concat result/strings (create_string_list 20)))
    (result/maps .add (create_map_chain 10))
  )
  
  (+ (result/genes .size) 
     (result/strings .size)
     (result/maps .size))
)

# Run benchmarks with timing
(import time)

(println "=== Allocation Stress Benchmarks ===")

(var start (time/now))
(var result (bench_gene_trees))
(var elapsed (- (time/now) start))
(println "Gene trees (1000 x 256 nodes): " elapsed "ms, result: " result)

(start = (time/now))
(result = (bench_strings))
(elapsed = (- (time/now) start))
(println "String operations (10000 strings): " elapsed "ms, result: " result)

(start = (time/now))
(result = (bench_maps))
(elapsed = (- (time/now) start))
(println "Map operations (100 maps x 50 entries): " elapsed "ms, result: " result)

(start = (time/now))
(result = (bench_arrays))
(elapsed = (- (time/now) start))
(println "Array operations (100 arrays x 10 items): " elapsed "ms, result: " result)

(start = (time/now))
(result = (bench_mixed))
(elapsed = (- (time/now) start))
(println "Mixed allocations: " elapsed "ms, result: " result)

(println "\nNote: Lower times indicate better performance")
(println "Object pooling should reduce allocation overhead significantly")