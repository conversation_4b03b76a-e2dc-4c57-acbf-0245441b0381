# Gene Benchmarks (Legacy)

⚠️ **DEPRECATED**: This directory contains legacy benchmark files.

## New Benchmark Organization

**The benchmark system has been reorganized for better maintainability and clarity.**

### 🔄 Migration Notice

Please use the new organized benchmark structure:

```bash
# Old way (deprecated)
./scripts/bench_suite
nim c -r src/benchmark/fibonacci.nim

# New way (recommended)
./benchmarks/runners/run_all.sh
./benchmarks/runners/run_computation.sh
```

### 📁 New Structure Location

All benchmarks have been moved to `benchmarks/` with proper categorization:

- **`benchmarks/computation/`** - Computational algorithms (fibonacci, arithmetic, loops)
- **`benchmarks/allocation/`** - Memory management and GC tests
- **`benchmarks/data_structures/`** - Array, map, string operations
- **`benchmarks/vm_internals/`** - VM optimizations and profiling
- **`benchmarks/comparison/`** - Cross-language performance comparisons
- **`benchmarks/runners/`** - Unified benchmark execution system

### 🚀 Quick Start with New System

```bash
# Run all benchmarks
./benchmarks/runners/run_all.sh

# Run specific category
./benchmarks/runners/run_computation.sh

# Run with profiling
./benchmarks/runners/run_all.sh -p

# Include language comparisons
./benchmarks/runners/run_all.sh --compare
```

### 📖 Documentation

- **Main README**: `benchmarks/README.md`
- **Migration Guide**: `BENCHMARK_MIGRATION.md`
- **Category READMEs**: Each category has detailed documentation

### 🔧 Legacy Files in This Directory

The files in this directory are maintained for backward compatibility but will be removed in a future release:

- `fibonacci.nim` → `benchmarks/computation/fibonacci.nim`
- `arithmetic*.nim` → `benchmarks/computation/`
- `tco.*` → `benchmarks/vm_internals/`
- `*_profile.nim` → `benchmarks/vm_internals/`

### ⏰ Deprecation Timeline

1. **Current**: New structure available, old structure still works
2. **Next Release**: Old structure marked as deprecated with warnings
3. **Future Release**: Old structure removed

### 🆘 Need Help?

- Check `BENCHMARK_MIGRATION.md` for detailed migration instructions
- See `benchmarks/README.md` for the new system overview
- Look at category-specific READMEs for detailed usage
- File issues for migration questions