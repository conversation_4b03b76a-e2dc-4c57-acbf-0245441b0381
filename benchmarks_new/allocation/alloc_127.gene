# Allocation benchmark for 127 iterations (current loop limit)
# Tests pooling effectiveness

# Create and destroy arrays repeatedly
(println "Array allocation test (127 iterations per batch, 10 batches)...")
(var total 0)
(for batch in (range 0 10)
  (for i in (range 0 127)
    (var arr [1 2 3 4 5])
    (total = (+ total 1))
  )
)
(println "Total arrays created: " total)

# Create and destroy maps repeatedly
(println "Map allocation test...")
(total = 0)
(for batch in (range 0 10)
  (for i in (range 0 127)
    (var m {^x 1 ^y 2 ^z 3})
    (total = (+ total 1))
  )
)
(println "Total maps created: " total)

# Mixed allocation pattern
(println "Mixed allocation test...")
(total = 0)
(for batch in (range 0 5)
  (for i in (range 0 127)
    (var a1 [i (* i 2)])
    (var m1 {^val i})
    (var s1 "test_string")
    (total = (+ total 1))
  )
)
(println "Total mixed iterations: " total)

(println "\nBenchmark complete - object pooling should reduce allocation overhead")
(println "Note: Loop currently limited to 127 iterations per range")