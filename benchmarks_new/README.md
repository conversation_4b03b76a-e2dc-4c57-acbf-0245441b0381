# Gene Benchmarks

This directory contains performance benchmarks for the Gene programming language, organized by functionality.

## Directory Structure

### 📊 Core Benchmark Categories

- **`computation/`** - Computational algorithms and mathematical operations
  - Fibonacci sequences, arithmetic operations, loops
  - Tests CPU-intensive operations and function call overhead

- **`allocation/`** - Memory allocation and garbage collection
  - Object creation/destruction, memory pools, GC stress tests
  - Tests memory management efficiency

- **`data_structures/`** - Data structure operations
  - Arrays, maps/dictionaries, strings, collections
  - Tests data access patterns and structure manipulation

- **`oop/`** - Object-oriented programming features
  - Class instantiation, method calls, inheritance
  - Tests OOP performance characteristics

- **`vm_internals/`** - Virtual machine internals and optimizations
  - Bytecode execution, frame management, tail call optimization
  - Tests VM implementation efficiency

### 🔧 Supporting Infrastructure

- **`comparison/`** - Cross-language performance comparisons
  - Equivalent benchmarks in Ruby, Python, JavaScript, etc.
  - Standardized comparison scripts

- **`scripts/`** - Benchmark execution and analysis tools
  - Profiling scripts, result analysis, automation tools

- **`runners/`** - Unified benchmark execution systems
  - Main benchmark runner, category-specific runners

## Quick Start

```bash
# Run all benchmarks
./benchmarks_new/runners/run_all.sh

# Run specific category
./benchmarks_new/runners/run_computation.sh

# Compare with other languages
./benchmarks_new/comparison/compare_all.sh

# Profile a specific benchmark
./benchmarks_new/scripts/profile.sh computation/fibonacci
```

## Adding New Benchmarks

1. Choose the appropriate category directory
2. Create your benchmark file (`.gene` for Gene code, `.nim` for Nim runners)
3. Add documentation explaining what the benchmark tests
4. Update the category's runner script
5. Add comparison implementations if relevant

## Benchmark Standards

- All benchmarks should include timing measurements
- Use consistent output format for automated analysis
- Include memory usage statistics where relevant
- Document expected performance characteristics
- Provide both Gene and native implementations for comparison

## Performance Tracking

Results are tracked in `results/` with historical data for regression detection.
See `scripts/analyze_trends.sh` for performance trend analysis.
